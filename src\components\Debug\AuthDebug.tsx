'use client';

import React, { useState } from 'react';
import { useAuth } from '@/context/AuthContext';

const AuthDebug: React.FC = () => {
  const { user, loading, error } = useAuth();
  const [showDebug, setShowDebug] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setShowDebug(!showDebug)}
        className="bg-gray-800 text-white px-3 py-2 rounded-md text-sm font-mono"
      >
        🐛 Auth Debug
      </button>
      
      {showDebug && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-300 rounded-lg shadow-lg p-4 w-80 max-h-96 overflow-auto">
          <h3 className="font-bold text-sm mb-2">Auth State Debug</h3>
          
          <div className="space-y-2 text-xs font-mono">
            <div>
              <strong>Loading:</strong> {loading ? 'true' : 'false'}
            </div>
            
            <div>
              <strong>Error:</strong> {error || 'null'}
            </div>
            
            <div>
              <strong>User:</strong> {user ? 'authenticated' : 'null'}
            </div>
            
            {user && (
              <div className="mt-2 p-2 bg-gray-50 rounded">
                <div><strong>ID:</strong> {user.id}</div>
                <div><strong>Email:</strong> {user.email}</div>
                <div><strong>Username:</strong> {user.username}</div>
                <div><strong>Display Name:</strong> {user.displayName}</div>
                <div><strong>Verified:</strong> {user.isVerified ? 'true' : 'false'}</div>
              </div>
            )}
            
            <div className="mt-2 p-2 bg-blue-50 rounded">
              <div className="font-bold mb-1">Firebase Config:</div>
              <div><strong>Project ID:</strong> {process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}</div>
              <div><strong>Auth Domain:</strong> {process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}</div>
              <div><strong>API Key:</strong> {process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? '✓ Set' : '✗ Missing'}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuthDebug;
