'use client';

import React, { useState, useEffect } from 'react';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { userService, linkService, followService, favoriteService } from '@/firebase/firestore';
import { User, LinkWithDetails } from '@/types';
import LinkCard from '@/components/UI/LinkCard';
import {
  ArrowLeftIcon,
  UserPlusIcon,
  UserMinusIcon,
  MapPinIcon,
  LinkIcon as LinkIconOutline,
  CalendarIcon,
  StarIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';

interface UserPageProps {
  params: Promise<{
    username: string;
  }>;
}

export default function UserPage({ params }: UserPageProps) {
  const { user: currentUser } = useAuth();
  const resolvedParams = React.use(params);
  const [user, setUser] = useState<User | null>(null);
  const [userLinks, setUserLinks] = useState<LinkWithDetails[]>([]);
  const [favorites, setFavorites] = useState<LinkWithDetails[]>([]);
  const [isFollowing, setIsFollowing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'links' | 'favorites'>('links');
  const [followLoading, setFollowLoading] = useState(false);

  // Load user profile and data
  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const userData = await userService.getByUsername(resolvedParams.username);
        if (!userData) {
          notFound();
          return;
        }
        
        setUser(userData);
        
        // Load user's links (TODO: implement getUserLinks in linkService)
        // For now, using empty array
        setUserLinks([]);
        
        // Load favorites if viewing own profile or public
        // setFavorites([]);
        
        // Check if current user follows this user
        if (currentUser && currentUser.id !== userData.id) {
          const following = await followService.isFollowing(currentUser.id, userData.id);
          setIsFollowing(following);
        }
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Fehler beim Laden des Profils');
      } finally {
        setLoading(false);
      }
    };

    loadUserProfile();
  }, [resolvedParams.username, currentUser]);

  const handleFollowToggle = async () => {
    if (!currentUser || !user || currentUser.id === user.id) return;
    
    try {
      setFollowLoading(true);
      
      if (isFollowing) {
        await followService.unfollow(currentUser.id, user.id);
        setIsFollowing(false);
      } else {
        await followService.follow(currentUser.id, user.id);
        setIsFollowing(true);
      }
      
    } catch (err) {
      console.error('Fehler beim Folgen/Entfolgen:', err);
    } finally {
      setFollowLoading(false);
    }
  };

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('de-DE', {
      year: 'numeric',
      month: 'long'
    }).format(date);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-48 mb-6"></div>
            <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
              <div className="flex items-start space-x-4">
                <div className="h-20 w-20 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-6 bg-gray-200 rounded w-48 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-32 mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-64"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {error || 'Benutzer nicht gefunden'}
            </h1>
            <Link
              href="/"
              className="text-blue-600 hover:text-blue-700"
            >
              Zur Startseite
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const isOwnProfile = currentUser?.id === user.id;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <div className="mb-6">
          <Link
            href="/"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Zur Startseite
          </Link>
        </div>

        {/* Profile Header */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between">
            <div className="flex items-start space-x-4">
              
              {/* Avatar */}
              <div className="flex-shrink-0">
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.displayName}
                    className="h-20 w-20 rounded-full object-cover border-2 border-gray-200"
                  />
                ) : (
                  <div className="h-20 w-20 rounded-full bg-gray-400 flex items-center justify-center text-white text-2xl font-bold">
                    {user.displayName.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
              
              {/* User Info */}
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl font-bold text-gray-900">{user.displayName}</h1>
                <p className="text-gray-600">@{user.username}</p>
                
                {user.bio && (
                  <p className="mt-2 text-gray-700">{user.bio}</p>
                )}
                
                <div className="mt-3 flex flex-wrap items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    Dabei seit {formatDate(user.createdAt)}
                  </div>
                  
                  {user.website && (
                    <a
                      href={user.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-blue-600 hover:text-blue-700"
                    >
                      <LinkIconOutline className="h-4 w-4 mr-1" />
                      Website
                    </a>
                  )}
                </div>
                
                {/* Stats */}
                <div className="mt-4 flex flex-wrap gap-6 text-sm">
                  <div>
                    <span className="font-semibold text-gray-900">{user.totalLinks || 0}</span>
                    <span className="text-gray-600 ml-1">Links</span>
                  </div>
                  <div>
                    <span className="font-semibold text-gray-900">{user.totalFollowers || 0}</span>
                    <span className="text-gray-600 ml-1">Follower</span>
                  </div>
                  <div>
                    <span className="font-semibold text-gray-900">{user.totalFollowing || 0}</span>
                    <span className="text-gray-600 ml-1">Folgt</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Action Buttons */}
            {currentUser && !isOwnProfile && (
              <div className="mt-4 md:mt-0">
                <button
                  onClick={handleFollowToggle}
                  disabled={followLoading}
                  className={`inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium transition-colors ${
                    isFollowing
                      ? 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                      : 'border-transparent text-white bg-blue-600 hover:bg-blue-700'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {followLoading ? (
                    <div className="h-4 w-4 mr-2 animate-spin border-2 border-current border-t-transparent rounded-full" />
                  ) : isFollowing ? (
                    <UserMinusIcon className="h-4 w-4 mr-2" />
                  ) : (
                    <UserPlusIcon className="h-4 w-4 mr-2" />
                  )}
                  {isFollowing ? 'Entfolgen' : 'Folgen'}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('links')}
                className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'links'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Eingereichte Links ({userLinks.length})
              </button>
              
              {(isOwnProfile || favorites.length > 0) && (
                <button
                  onClick={() => setActiveTab('favorites')}
                  className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'favorites'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center">
                    <HeartIcon className="h-4 w-4 mr-1" />
                    Favoriten ({favorites.length})
                  </div>
                </button>
              )}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-4">
          {activeTab === 'links' ? (
            userLinks.length > 0 ? (
              userLinks.map((link) => (
                <LinkCard
                  key={link.id}
                  link={link}
                  showCategory={true}
                />
              ))
            ) : (
              <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
                <LinkIconOutline className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {isOwnProfile ? 'Du hast noch keine Links eingereicht' : `${user.displayName} hat noch keine Links eingereicht`}
                </h3>
                <p className="text-gray-600 mb-6">
                  {isOwnProfile 
                    ? 'Teile deinen ersten hochwertigen Link mit der Community!'
                    : 'Schaue später nochmal vorbei.'
                  }
                </p>
                {isOwnProfile && (
                  <Link
                    href="/submit"
                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Ersten Link einreichen
                  </Link>
                )}
              </div>
            )
          ) : (
            favorites.length > 0 ? (
              favorites.map((link) => (
                <LinkCard
                  key={link.id}
                  link={link}
                  showCategory={true}
                />
              ))
            ) : (
              <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
                <HeartIconSolid className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {isOwnProfile ? 'Du hast noch keine Favoriten' : 'Keine öffentlichen Favoriten'}
                </h3>
                <p className="text-gray-600">
                  {isOwnProfile 
                    ? 'Markiere interessante Links als Favoriten!'
                    : 'Dieser Benutzer hat seine Favoriten nicht öffentlich geteilt.'
                  }
                </p>
              </div>
            )
          )}
        </div>

      </div>
    </div>
  );
} 