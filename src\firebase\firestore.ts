import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  increment,
  writeBatch,
  serverTimestamp,
  DocumentSnapshot,
  QueryDocumentSnapshot,
  DocumentData,
  Timestamp,
  setDoc
} from 'firebase/firestore';
import { db } from './config';
import {
  User,
  Link,
  Category,
  Rating,
  Comment,
  Follow,
  Favorite,
  Report,
  CreateLinkForm,
  CreateCategoryForm,
  LinkWithDetails,
  PaginatedResponse
} from '@/types';
import slugify from 'slugify';

// Collection names
export const COLLECTIONS = {
  USERS: 'users',
  LINKS: 'links',
  CATEGORIES: 'categories',
  RATINGS: 'ratings',
  COMMENTS: 'comments',
  FOLLOWS: 'follows',
  FAVORITES: 'favorites',
  REPORTS: 'reports'
} as const;

// Helper function to convert Firestore timestamp to Date
const convertTimestamp = (timestamp: any): Date => {
  if (timestamp instanceof Timestamp) {
    return timestamp.toDate();
  }
  return timestamp;
};

// Helper function to serialize Firestore document
const serializeDoc = <T>(doc: QueryDocumentSnapshot<DocumentData> | DocumentSnapshot<DocumentData>): T => {
  const data = doc.data();
  if (!data) throw new Error('Document not found');
  
  // Convert all timestamp fields to Date objects
  const serialized: any = { id: doc.id, ...data };
  Object.keys(serialized).forEach(key => {
    if (serialized[key] instanceof Timestamp) {
      serialized[key] = serialized[key].toDate();
    }
  });
  
  return serialized as T;
};

// User operations
export const userService = {
  async create(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>, customId?: string): Promise<string> {
    console.log('👤 UserService.create called with data:', userData);
    console.log('👤 Custom ID provided:', customId);
    
    if (customId) {
      // Use specific document ID (for Firebase Auth UID)
      await setDoc(doc(db, COLLECTIONS.USERS, customId), {
        ...userData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        totalLinks: 0,
        totalFollowers: 0,
        totalFollowing: 0
      });
      console.log('👤 User created with custom ID:', customId);
      return customId;
    } else {
      // Auto-generate ID
      const docRef = await addDoc(collection(db, COLLECTIONS.USERS), {
        ...userData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        totalLinks: 0,
        totalFollowers: 0,
        totalFollowing: 0
      });
      console.log('👤 User created with auto-generated ID:', docRef.id);
      return docRef.id;
    }
  },

  async getById(id: string): Promise<User | null> {
    console.log('👤 UserService.getById called with id:', id);
    try {
      const docSnap = await getDoc(doc(db, COLLECTIONS.USERS, id));
      console.log('👤 Document exists:', docSnap.exists());
      if (docSnap.exists()) {
        const userData = serializeDoc<User>(docSnap);
        console.log('👤 User data loaded:', userData);
        return userData;
      } else {
        console.log('👤 No user found with id:', id);
        return null;
      }
    } catch (error) {
      console.error('🚨 Error in userService.getById:', error);
      return null;
    }
  },

  async getByUsername(username: string): Promise<User | null> {
    const q = query(collection(db, COLLECTIONS.USERS), where('username', '==', username));
    const querySnapshot = await getDocs(q);
    return querySnapshot.empty ? null : serializeDoc<User>(querySnapshot.docs[0]);
  },

  async update(id: string, data: Partial<User>): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.USERS, id), {
      ...data,
      updatedAt: serverTimestamp()
    });
  },

  async checkUsernameExists(username: string): Promise<boolean> {
    const user = await this.getByUsername(username);
    return user !== null;
  },

  async getTotalCount(): Promise<number> {
    const querySnapshot = await getDocs(collection(db, COLLECTIONS.USERS));
    return querySnapshot.size;
  }
};

// Category operations
export const categoryService = {
  async create(categoryData: CreateCategoryForm, userId: string): Promise<string> {
    const slug = slugify(categoryData.name, { lower: true, strict: true });
    
    const docRef = await addDoc(collection(db, COLLECTIONS.CATEGORIES), {
      ...categoryData,
      slug,
      isActive: true,
      createdBy: userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      totalLinks: 0
    });
    return docRef.id;
  },

  async getById(id: string): Promise<Category | null> {
    const docSnap = await getDoc(doc(db, COLLECTIONS.CATEGORIES, id));
    return docSnap.exists() ? serializeDoc<Category>(docSnap) : null;
  },

  async getAll(): Promise<Category[]> {
    const q = query(
      collection(db, COLLECTIONS.CATEGORIES),
      where('isActive', '==', true),
      orderBy('name')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Category>(doc));
  },

  async getBySlug(slug: string): Promise<Category | null> {
    const q = query(collection(db, COLLECTIONS.CATEGORIES), where('slug', '==', slug));
    const querySnapshot = await getDocs(q);
    return querySnapshot.empty ? null : serializeDoc<Category>(querySnapshot.docs[0]);
  },

  async checkSlugExists(slug: string): Promise<boolean> {
    const category = await this.getBySlug(slug);
    return category !== null;
  },

  async incrementLinkCount(categoryId: string): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.CATEGORIES, categoryId), {
      totalLinks: increment(1)
    });
  },

  async update(id: string, data: Partial<Category>): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.CATEGORIES, id), {
      ...data,
      updatedAt: serverTimestamp()
    });
  },

  async delete(id: string): Promise<void> {
    await deleteDoc(doc(db, COLLECTIONS.CATEGORIES, id));
  },

  async getRecent(limitCount: number = 5): Promise<Category[]> {
    const q = query(
      collection(db, COLLECTIONS.CATEGORIES),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Category>(doc));
  },

  async getByUser(userId: string): Promise<Category[]> {
    const q = query(
      collection(db, COLLECTIONS.CATEGORIES),
      where('createdBy', '==', userId),
      where('isActive', '==', true),
      orderBy('name')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Category>(doc));
  }
};

// Link operations
export const linkService = {
  async create(linkData: CreateLinkForm, userId: string): Promise<string> {
    // Extract domain from URL
    const url = new URL(linkData.url);
    const domain = url.hostname;

    const docRef = await addDoc(collection(db, COLLECTIONS.LINKS), {
      title: linkData.title,
      url: linkData.url,
      description: linkData.description || '',
      domain,
      categoryId: linkData.categoryId,
      submittedBy: userId,
      isApproved: false, // Requires approval by default
      isFeatured: false,
      tags: linkData.tags || [],
      submittedAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      averageRating: 0,
      totalRatings: 0,
      totalComments: 0
    });

    // Increment category link count
    await categoryService.incrementLinkCount(linkData.categoryId);

    return docRef.id;
  },

  async getById(id: string): Promise<Link | null> {
    const docSnap = await getDoc(doc(db, COLLECTIONS.LINKS, id));
    return docSnap.exists() ? serializeDoc<Link>(docSnap) : null;
  },

  async getByCategory(
    categoryId: string,
    pageSize: number = 20,
    lastDoc?: DocumentSnapshot
  ): Promise<PaginatedResponse<LinkWithDetails>> {
    let q = query(
      collection(db, COLLECTIONS.LINKS),
      where('categoryId', '==', categoryId),
      where('isApproved', '==', true),
      orderBy('submittedAt', 'desc'),
      limit(pageSize)
    );

    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
    
    // Get additional data for each link (category, submitter)
    const linksWithDetails = await Promise.all(
      links.map(async (link) => {
        const [category, submitter] = await Promise.all([
          categoryService.getById(link.categoryId),
          userService.getById(link.submittedBy)
        ]);

        // Use placeholder category if missing
        const effectiveCategory = category || {
          id: 'unknown',
          name: 'Unbekannte Kategorie',
          slug: 'unknown',
          description: 'Kategorie nicht gefunden',
          isActive: false,
          createdBy: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          totalLinks: 0
        };

        // Use placeholder submitter if missing
        const effectiveSubmitter = submitter || {
          id: 'unknown',
          username: 'unknown-user',
          displayName: 'Unbekannter Benutzer',
          email: '<EMAIL>',
          avatar: undefined,
          bio: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          totalLinks: 0,
          totalFollowers: 0,
          totalFollowing: 0
        };

        return {
          ...link,
          category: effectiveCategory,
          submitter: {
            id: effectiveSubmitter.id,
            username: effectiveSubmitter.username,
            displayName: effectiveSubmitter.displayName,
            avatar: effectiveSubmitter.avatar
          }
        } as LinkWithDetails;
      })
    );

    return {
      data: linksWithDetails,
      pagination: {
        page: 1, // This would need to be calculated based on the pagination state
        limit: pageSize,
        total: linksWithDetails.length, // This would need to be calculated separately
        totalPages: Math.ceil(linksWithDetails.length / pageSize),
        hasNext: querySnapshot.docs.length === pageSize,
        hasPrev: false // This would depend on current page
      }
    };
  },

  async getPending(): Promise<LinkWithDetails[]> {
    const q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', false),
      orderBy('submittedAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
    
    // Get additional data for each link (category, submitter)
    const linksWithDetails = await Promise.all(
      links.map(async (link) => {
        const [category, submitter] = await Promise.all([
          categoryService.getById(link.categoryId),
          userService.getById(link.submittedBy)
        ]);

        // Use placeholder category if missing
        const effectiveCategory = category || {
          id: 'unknown',
          name: 'Unbekannte Kategorie',
          slug: 'unknown',
          description: 'Kategorie nicht gefunden',
          isActive: false,
          createdBy: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          totalLinks: 0
        };

        // Use placeholder submitter if missing
        const effectiveSubmitter = submitter || {
          id: 'unknown',
          username: 'unknown-user',
          displayName: 'Unbekannter Benutzer',
          email: '<EMAIL>',
          avatar: undefined,
          bio: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          totalLinks: 0,
          totalFollowers: 0,
          totalFollowing: 0
        };

        return {
          ...link,
          category: effectiveCategory,
          submitter: {
            id: effectiveSubmitter.id,
            username: effectiveSubmitter.username,
            displayName: effectiveSubmitter.displayName,
            avatar: effectiveSubmitter.avatar
          }
        } as LinkWithDetails;
      })
    );

    return linksWithDetails;
  },

  async approve(linkId: string): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
      isApproved: true,
      approvedAt: serverTimestamp()
    });
  },

  async reject(linkId: string): Promise<void> {
    // For now, we'll delete rejected links. In production, you might want to keep them with a rejected status
    await deleteDoc(doc(db, COLLECTIONS.LINKS, linkId));
  },

  async feature(linkId: string, featured: boolean): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
      isFeatured: featured
    });
  },

  async getRecentApproved(limitCount: number = 5): Promise<LinkWithDetails[]> {
    const q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true),
      where('approvedAt', '!=', null),
      orderBy('approvedAt', 'desc'),
      orderBy('submittedAt', 'desc'),
      limit(limitCount)
    );

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));

    // Get additional data for each link (category, submitter)
    const linksWithDetails = await Promise.all(
      links.map(async (link) => {
        const [category, submitter] = await Promise.all([
          categoryService.getById(link.categoryId),
          userService.getById(link.submittedBy)
        ]);

        return {
          ...link,
          category: category || { id: 'unknown', name: 'Unbekannte Kategorie', slug: 'unknown', description: '', isActive: false, createdBy: '', createdAt: new Date(), updatedAt: new Date(), totalLinks: 0 },
          submitter: submitter ? { id: submitter.id, username: submitter.username, displayName: submitter.displayName, avatar: submitter.avatar } : { id: 'unknown', username: 'unknown-user', displayName: 'Unbekannter Benutzer', avatar: undefined }
        } as LinkWithDetails;
      })
    );

    return linksWithDetails;
  },

  async getAll(options: {
    sortBy?: 'newest' | 'oldest' | 'rating' | 'popular';
    timeRange?: 'day' | 'week' | 'month' | 'year' | 'all';
    domain?: string;
    tags?: string[];
    pageSize?: number;
    lastDoc?: DocumentSnapshot;
  } = {}): Promise<PaginatedResponse<LinkWithDetails>> {
    const {
      sortBy = 'newest',
      timeRange = 'all',
      domain,
      tags,
      pageSize = 20,
      lastDoc
    } = options;

    let q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true)
    );

    // Add time range filter
    if (timeRange !== 'all') {
      const now = new Date();
      let startDate: Date;

      switch (timeRange) {
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case 'year':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(0);
      }

      q = query(q, where('submittedAt', '>=', startDate));
    }

    // Add domain filter
    if (domain) {
      q = query(q, where('domain', '==', domain));
    }

    // Add tags filter (array-contains-any for multiple tags)
    if (tags && tags.length > 0) {
      q = query(q, where('tags', 'array-contains-any', tags));
    }

    // Add sorting
    switch (sortBy) {
      case 'newest':
        q = query(q, orderBy('submittedAt', 'desc'));
        break;
      case 'oldest':
        q = query(q, orderBy('submittedAt', 'asc'));
        break;
      case 'rating':
        q = query(q, orderBy('averageRating', 'desc'), orderBy('submittedAt', 'desc'));
        break;
      case 'popular':
        q = query(q, orderBy('totalRatings', 'desc'), orderBy('submittedAt', 'desc'));
        break;
    }

    // Add pagination
    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    q = query(q, limit(pageSize + 1)); // Get one extra to check if there are more

    const querySnapshot = await getDocs(q);
    const docs = querySnapshot.docs;

    // Check if there are more results
    const hasNext = docs.length > pageSize;
    const actualDocs = hasNext ? docs.slice(0, pageSize) : docs;

    const links = actualDocs.map(doc => serializeDoc<Link>(doc));

    // Get additional data for each link (category, submitter)
    const linksWithDetails = await Promise.all(
      links.map(async (link) => {
        const [category, submitter] = await Promise.all([
          categoryService.getById(link.categoryId),
          userService.getById(link.submittedBy)
        ]);

        return {
          ...link,
          category: category || { id: 'unknown', name: 'Unbekannte Kategorie', slug: 'unknown', description: '', isActive: false, createdBy: '', createdAt: new Date(), updatedAt: new Date(), totalLinks: 0 },
          submitter: submitter ? { id: submitter.id, username: submitter.username, displayName: submitter.displayName, avatar: submitter.avatar } : { id: 'unknown', username: 'unknown-user', displayName: 'Unbekannter Benutzer', avatar: undefined }
        } as LinkWithDetails;
      })
    );

    return {
      data: linksWithDetails,
      pagination: {
        page: 1, // This would need to be calculated based on lastDoc
        limit: pageSize,
        total: linksWithDetails.length, // This is approximate
        totalPages: 1, // This would need proper calculation
        hasNext,
        hasPrev: !!lastDoc
      }
    };
  },

  async search(searchQuery: string, options: {
    sortBy?: 'newest' | 'oldest' | 'rating' | 'popular';
    categoryId?: string;
    pageSize?: number;
    lastDoc?: DocumentSnapshot;
  } = {}): Promise<PaginatedResponse<LinkWithDetails>> {
    const {
      sortBy = 'newest',
      categoryId,
      pageSize = 20,
      lastDoc
    } = options;

    // For now, we'll implement a simple search that looks for the query in title and description
    // In a production app, you'd want to use a proper search service like Algolia or Elasticsearch

    let q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true)
    );

    // Add category filter if specified
    if (categoryId) {
      q = query(q, where('categoryId', '==', categoryId));
    }

    // Add sorting
    switch (sortBy) {
      case 'newest':
        q = query(q, orderBy('submittedAt', 'desc'));
        break;
      case 'oldest':
        q = query(q, orderBy('submittedAt', 'asc'));
        break;
      case 'rating':
        q = query(q, orderBy('averageRating', 'desc'), orderBy('submittedAt', 'desc'));
        break;
      case 'popular':
        q = query(q, orderBy('totalRatings', 'desc'), orderBy('submittedAt', 'desc'));
        break;
    }

    // Add pagination
    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    q = query(q, limit(pageSize * 3)); // Get more results to filter client-side

    const querySnapshot = await getDocs(q);
    const docs = querySnapshot.docs;

    const links = docs.map(doc => serializeDoc<Link>(doc));

    // Filter results based on search query (client-side for now)
    const searchTerms = searchQuery.toLowerCase().split(' ').filter(term => term.length > 0);
    const filteredLinks = links.filter(link => {
      const searchableText = [
        link.title,
        link.description || '',
        link.domain,
        ...(link.tags || [])
      ].join(' ').toLowerCase();

      return searchTerms.every(term => searchableText.includes(term));
    }).slice(0, pageSize);

    // Get additional data for each link (category, submitter)
    const linksWithDetails = await Promise.all(
      filteredLinks.map(async (link) => {
        const [category, submitter] = await Promise.all([
          categoryService.getById(link.categoryId),
          userService.getById(link.submittedBy)
        ]);

        return {
          ...link,
          category: category || { id: 'unknown', name: 'Unbekannte Kategorie', slug: 'unknown', description: '', isActive: false, createdBy: '', createdAt: new Date(), updatedAt: new Date(), totalLinks: 0 },
          submitter: submitter ? { id: submitter.id, username: submitter.username, displayName: submitter.displayName, avatar: submitter.avatar } : { id: 'unknown', username: 'unknown-user', displayName: 'Unbekannter Benutzer', avatar: undefined }
        } as LinkWithDetails;
      })
    );

    return {
      data: linksWithDetails,
      pagination: {
        page: 1,
        limit: pageSize,
        total: linksWithDetails.length,
        totalPages: 1,
        hasNext: linksWithDetails.length === pageSize, // Approximate
        hasPrev: !!lastDoc
      }
    };
  },

  async getPending(): Promise<LinkWithDetails[]> {
    const q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', false),
      orderBy('submittedAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));

    // Get additional data for each link (category, submitter)
    const linksWithDetails = await Promise.all(
      links.map(async (link) => {
        const [category, submitter] = await Promise.all([
          categoryService.getById(link.categoryId),
          userService.getById(link.submittedBy)
        ]);

        return {
          ...link,
          category: category || { id: 'unknown', name: 'Unbekannte Kategorie', slug: 'unknown', description: '', isActive: false, createdBy: '', createdAt: new Date(), updatedAt: new Date(), totalLinks: 0 },
          submitter: submitter ? { id: submitter.id, username: submitter.username, displayName: submitter.displayName, avatar: submitter.avatar } : { id: 'unknown', username: 'unknown-user', displayName: 'Unbekannter Benutzer', avatar: undefined }
        } as LinkWithDetails;
      })
    );

    return linksWithDetails;
  },

  async getRecentSubmitted(limitCount: number = 5): Promise<LinkWithDetails[]> {
    const q = query(
      collection(db, COLLECTIONS.LINKS),
      orderBy('submittedAt', 'desc'),
      limit(limitCount)
    );

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));

    // Get additional data for each link (category, submitter)
    const linksWithDetails = await Promise.all(
      links.map(async (link) => {
        const [category, submitter] = await Promise.all([
          categoryService.getById(link.categoryId),
          userService.getById(link.submittedBy)
        ]);

        return {
          ...link,
          category: category || { id: 'unknown', name: 'Unbekannte Kategorie', slug: 'unknown', description: '', isActive: false, createdBy: '', createdAt: new Date(), updatedAt: new Date(), totalLinks: 0 },
          submitter: submitter ? { id: submitter.id, username: submitter.username, displayName: submitter.displayName, avatar: submitter.avatar } : { id: 'unknown', username: 'unknown-user', displayName: 'Unbekannter Benutzer', avatar: undefined }
        } as LinkWithDetails;
      })
    );

    return linksWithDetails;
  },

  async getTotalApprovedCount(): Promise<number> {
    const q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true)
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.size;
  },

  async getRecentSubmitted(limitCount: number = 5): Promise<LinkWithDetails[]> {
    const q = query(
      collection(db, COLLECTIONS.LINKS),
      orderBy('submittedAt', 'desc'),
      limit(limitCount)
    );

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
    
    // Get additional data for each link (category, submitter)
    const linksWithDetails = await Promise.all(
      links.map(async (link) => {
        const [category, submitter] = await Promise.all([
          categoryService.getById(link.categoryId),
          userService.getById(link.submittedBy)
        ]);

        return {
          ...link,
          category: category || { id: 'unknown', name: 'Unbekannte Kategorie', slug: 'unknown', description: '', isActive: false, createdBy: '', createdAt: new Date(), updatedAt: new Date(), totalLinks: 0 },
          submitter: submitter ? { id: submitter.id, username: submitter.username, displayName: submitter.displayName, avatar: submitter.avatar } : { id: 'unknown', username: 'unknown-user', displayName: 'Unbekannter Benutzer', avatar: undefined }
        } as LinkWithDetails;
      })
    );

    return linksWithDetails;
  },

  async getTotalApprovedCount(): Promise<number> {
    const q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.size;
  },

  async getTotalCount(): Promise<number> {
    const querySnapshot = await getDocs(collection(db, COLLECTIONS.LINKS));
    return querySnapshot.size;
  }
};

// Rating operations
export const ratingService = {
  async create(linkId: string, userId: string, rating: number): Promise<void> {
    const batch = writeBatch(db);

    // Check if user already has a rating for this link
    const existingRatingQuery = query(
      collection(db, COLLECTIONS.RATINGS),
      where('linkId', '==', linkId),
      where('userId', '==', userId)
    );
    const existingRatingSnapshot = await getDocs(existingRatingQuery);

    if (!existingRatingSnapshot.empty) {
      // Update existing rating
      const existingRatingDoc = existingRatingSnapshot.docs[0];
      batch.update(existingRatingDoc.ref, {
        rating,
        updatedAt: serverTimestamp()
      });
    } else {
      // Create new rating
      const ratingRef = doc(collection(db, COLLECTIONS.RATINGS));
      batch.set(ratingRef, {
        linkId,
        userId,
        rating,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // Increment total ratings count only for new ratings
      const linkRef = doc(db, COLLECTIONS.LINKS, linkId);
      batch.update(linkRef, {
        totalRatings: increment(1)
      });
    }

    await batch.commit();

    // Recalculate average rating
    await this.updateAverageRating(linkId);
  },

  async updateAverageRating(linkId: string): Promise<void> {
    const ratingsQuery = query(
      collection(db, COLLECTIONS.RATINGS),
      where('linkId', '==', linkId)
    );
    const ratingsSnapshot = await getDocs(ratingsQuery);
    
    if (ratingsSnapshot.empty) {
      // No ratings
      await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
        averageRating: 0,
        totalRatings: 0
      });
      return;
    }

    const ratings = ratingsSnapshot.docs.map(doc => doc.data().rating);
    const averageRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;

    await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
      averageRating,
      totalRatings: ratings.length
    });
  },

  async getUserRating(linkId: string, userId: string): Promise<Rating | null> {
    const q = query(
      collection(db, COLLECTIONS.RATINGS),
      where('linkId', '==', linkId),
      where('userId', '==', userId)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.empty ? null : serializeDoc<Rating>(querySnapshot.docs[0]);
  }
};

// Comment operations
export const commentService = {
  async create(linkId: string, userId: string, content: string, parentId?: string): Promise<string> {
    const docRef = await addDoc(collection(db, COLLECTIONS.COMMENTS), {
      linkId,
      userId,
      content,
      parentId: parentId || null,
      isApproved: true, // Auto-approve for now - change back to false for production
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // Increment link's comment count
    await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
      totalComments: increment(1)
    });

    return docRef.id;
  },

  async getByLink(linkId: string): Promise<Comment[]> {
    console.log('💬 CommentService.getByLink called with linkId:', linkId);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.COMMENTS),
        where('linkId', '==', linkId),
        where('isApproved', '==', true),
        orderBy('createdAt', 'asc')
      );
      
      console.log('💬 Executing Firestore query...');
      const querySnapshot = await getDocs(q);
      console.log('💬 Query executed, docs found:', querySnapshot.docs.length);
      
      const comments = querySnapshot.docs.map(doc => {
        console.log('💬 Processing comment doc:', doc.id, doc.data());
        return serializeDoc<Comment>(doc);
      });
      console.log('💬 Serialized comments:', comments);

      // Load author information for each comment
      console.log('💬 Loading author information...');
      const commentsWithAuthors = await Promise.all(
        comments.map(async (comment) => {
          console.log('💬 Loading author for comment:', comment.id, 'userId:', comment.userId);
          const author = await userService.getById(comment.userId);
          console.log('💬 Author loaded:', author);
          
          return {
            ...comment,
            author: author ? {
              id: author.id,
              username: author.username,
              displayName: author.displayName,
              avatar: author.avatar
            } : {
              id: 'unknown',
              username: 'unknown-user',
              displayName: 'Unbekannter Benutzer',
              avatar: undefined
            }
          };
        })
      );

      console.log('💬 Final comments with authors:', commentsWithAuthors);
      return commentsWithAuthors;
    } catch (error) {
      console.error('🚨 Error in commentService.getByLink:', error);
      throw error;
    }
  },

  async getByLinkNested(linkId: string): Promise<Comment[]> {
    const comments = await this.getByLink(linkId);
    
    // Organize comments into nested structure
    const commentMap = new Map();
    const rootComments: any[] = [];

    // First pass: create map and identify root comments
    comments.forEach(comment => {
      const commentWithReplies = { ...comment, replies: [] };
      commentMap.set(comment.id, commentWithReplies);
      if (!comment.parentId) {
        rootComments.push(commentWithReplies);
      }
    });

    // Second pass: nest replies
    comments.forEach(comment => {
      if (comment.parentId && commentMap.has(comment.parentId)) {
        const parentComment = commentMap.get(comment.parentId);
        const childComment = commentMap.get(comment.id);
        parentComment.replies.push(childComment);
      }
    });

    return rootComments;
  },

  async approve(commentId: string): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.COMMENTS, commentId), {
      isApproved: true
    });
  }
};

// Follow operations
export const followService = {
  async follow(followerId: string, followingId: string): Promise<void> {
    const batch = writeBatch(db);

    // Create follow relationship
    const followRef = doc(collection(db, COLLECTIONS.FOLLOWS));
    batch.set(followRef, {
      followerId,
      followingId,
      createdAt: serverTimestamp()
    });

    // Update user stats
    const followerRef = doc(db, COLLECTIONS.USERS, followerId);
    const followingRef = doc(db, COLLECTIONS.USERS, followingId);
    
    batch.update(followerRef, { totalFollowing: increment(1) });
    batch.update(followingRef, { totalFollowers: increment(1) });

    await batch.commit();
  },

  async unfollow(followerId: string, followingId: string): Promise<void> {
    const q = query(
      collection(db, COLLECTIONS.FOLLOWS),
      where('followerId', '==', followerId),
      where('followingId', '==', followingId)
    );
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const batch = writeBatch(db);
      
      // Delete follow relationship
      batch.delete(querySnapshot.docs[0].ref);

      // Update user stats
      const followerRef = doc(db, COLLECTIONS.USERS, followerId);
      const followingRef = doc(db, COLLECTIONS.USERS, followingId);
      
      batch.update(followerRef, { totalFollowing: increment(-1) });
      batch.update(followingRef, { totalFollowers: increment(-1) });

      await batch.commit();
    }
  },

  async isFollowing(followerId: string, followingId: string): Promise<boolean> {
    const q = query(
      collection(db, COLLECTIONS.FOLLOWS),
      where('followerId', '==', followerId),
      where('followingId', '==', followingId)
    );
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  }
};

// Favorite operations
export const favoriteService = {
  async add(userId: string, linkId: string): Promise<void> {
    await addDoc(collection(db, COLLECTIONS.FAVORITES), {
      userId,
      linkId,
      createdAt: serverTimestamp()
    });
  },

  async remove(userId: string, linkId: string): Promise<void> {
    const q = query(
      collection(db, COLLECTIONS.FAVORITES),
      where('userId', '==', userId),
      where('linkId', '==', linkId)
    );
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      await deleteDoc(querySnapshot.docs[0].ref);
    }
  },

  async isFavorited(userId: string, linkId: string): Promise<boolean> {
    const q = query(
      collection(db, COLLECTIONS.FAVORITES),
      where('userId', '==', userId),
      where('linkId', '==', linkId)
    );
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  },

  async getUserFavorites(userId: string): Promise<Link[]> {
    const q = query(
      collection(db, COLLECTIONS.FAVORITES),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    const linkIds = querySnapshot.docs.map(doc => doc.data().linkId);
    const links = await Promise.all(
      linkIds.map(linkId => linkService.getById(linkId))
    );
    
    return links.filter(link => link !== null) as Link[];
  }
};

// Report operations
export const reportService = {
  async create(
    targetType: 'link' | 'comment',
    targetId: string,
    reportedBy: string,
    reason: string,
    customReason?: string,
    description?: string
  ): Promise<string> {
    const docRef = await addDoc(collection(db, COLLECTIONS.REPORTS), {
      reportedBy,
      targetType,
      targetId,
      reason,
      customReason: customReason || null,
      description: description || null,
      status: 'pending',
      createdAt: serverTimestamp()
    });
    
    return docRef.id;
  },

  async getByUser(userId: string): Promise<Report[]> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      where('reportedBy', '==', userId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Report>(doc));
  },

  async getPending(): Promise<Report[]> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      where('status', '==', 'pending'),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Report>(doc));
  },

  async getAll(): Promise<Report[]> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Report>(doc));
  },

  async updateStatus(
    reportId: string,
    status: 'reviewed' | 'resolved' | 'dismissed',
    reviewedBy: string,
    reviewNotes?: string
  ): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.REPORTS, reportId), {
      status,
      reviewedAt: serverTimestamp(),
      reviewedBy,
      reviewNotes: reviewNotes || null
    });
  },

  async hasUserReported(userId: string, targetType: string, targetId: string): Promise<boolean> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      where('reportedBy', '==', userId),
      where('targetType', '==', targetType),
      where('targetId', '==', targetId)
    );
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  },

  async getReportsForTarget(targetType: 'link' | 'comment', targetId: string): Promise<Report[]> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      where('targetType', '==', targetType),
      where('targetId', '==', targetId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Report>(doc));
  },

  async getRecent(limitCount: number = 5): Promise<Report[]> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Report>(doc));
  }
}; 